
#include <ti_msp_dl_config.h>
#include <LED.h>
#include <key.h>
#include <uart.h>
#include <timer.h>
#include <delay.h>
#include <oled.h>
#include <oled_user.h>
#include <User_IR_Sensor.h>
#include "User_Control.h"
#include "counting.h"

unsigned char Mode=0x50; 	//???????????
int main(void)
{
	SYSCFG_DL_init();
	Timer_Start();
	OLED_Init();
	ADC_Init();
	ADC_start();
	LED1_OFF;LED2_OFF;
				
	OLEDLCD_Refresh_AllGDRAM();
	while (1) 
	{
		Display_ADC();
		Binarization_Display(10,0,1);
		Display_Counting_Info();  // 新增：显示圈数和拐角数信息
		OLEDLCD_Refresh_AllGDRAM();
		delay_ms(1);  // ???????????????????
	if(KEYR_Flag==1) //???????????????KR????
	{
		LED0_ON;  // ?????????????LED???????
		if(Mode==0x50) //????????????????????
		{
			Mode=0xA1;
			LED1_ON;  // ??????????
		}
		else //????????????????????
		{
			Mode=0x50;
			LED1_OFF; // ???????????
		}
		KEYR_Flag=0;//??????λ????????μ????
		delay_ms(200); // ?????????LED????
		LED0_OFF; // ???LED
	}
		
	}
}

unsigned char LED_Flag=0;
void SYS_TIMER_INST_IRQHandler(void)
{

	switch (DL_TimerG_getPendingInterrupt(SYS_TIMER_INST)) 
	{
		case DL_TIMER_IIDX_ZERO:
			
			if(ADC_Flag==1)	{ DL_ADC12_startConversion(ADC12_0_INST);  ADC_Flag=0;}//??????
			if(ADC1_Flag==1){ DL_ADC12_startConversion(ADC12_1_INST);  ADC1_Flag=0;}//??????
			
			Control();
			Check_Key();    //??鰴????
			counting();
			keycontrol();   //??????????????????
			LED_Flag++;
			if(LED_Flag==100)LED0_OFF;
			if(LED_Flag==200){LED0_ON;LED_Flag=0;};
			break;
		default:
			break;
	}
}

